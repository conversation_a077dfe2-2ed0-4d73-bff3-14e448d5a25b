import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, CreateBucketCommand, HeadBucketCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly endpoint: string;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = this.configService.get<string>('S3_BUCKET') || 'warfront-nations';

    const region = this.configService.get<string>('S3_REGION') || 'fsn1'; // Default to fsn1
    const accessKeyId = this.configService.get<string>('S3_ACCESS_KEY');
    const secretAccessKey = this.configService.get<string>('S3_SECRET_KEY');

    if (!region || !accessKeyId || !secretAccessKey) {
      throw new Error('Missing required S3 configuration');
    }

    // Build the correct endpoint format: https://<bucket>.<region>.your-objectstorage.com
    const endpoint = `https://${this.bucketName}.${region}.your-objectstorage.com`;

    // Store the endpoint for URL generation
    this.endpoint = endpoint;

    this.s3Client = new S3Client({
      endpoint,
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      forcePathStyle: false, // Use virtual-hosted-style URLs
      useAccelerateEndpoint: false,
      useDualstackEndpoint: false,
    });

    this.logger.log(`S3 client initialized with endpoint: ${endpoint}`);
    this.logger.log(`Region: ${region}, Bucket: ${this.bucketName}`);
  }

  /**
   * Upload a file to S3 Object Storage
   */
  async uploadFile(
    key: string,
    buffer: Buffer,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    try {
      this.logger.log(`Attempting to upload file: ${key} to bucket: ${this.bucketName}`);
      this.logger.log(`Endpoint: ${this.configService.get<string>('S3_ENDPOINT')}`);

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: metadata,
        // Remove ACL as many S3-compatible services don't support it
      });

      const result = await this.s3Client.send(command);
      this.logger.log(`Upload result:`, result);

      // Return the public URL using virtual-hosted-style format
      const publicUrl = this.generateVirtualHostedUrl(key);

      this.logger.log(`File uploaded successfully: ${key}`);
      return publicUrl;
    } catch (error) {
      this.logger.error(`Failed to upload file ${key}:`, error);
      this.logger.error(`Error details:`, {
        message: error.message,
        statusCode: error.$metadata?.httpStatusCode,
        requestId: error.$metadata?.requestId,
      });
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Delete a file from S3 Object Storage
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`File deleted successfully: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}:`, error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Generate a presigned URL for temporary access to a private file
   */
  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const signedUrl = await getSignedUrl(this.s3Client, command, { expiresIn });
      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for ${key}:`, error);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Extract the key from a full URL
   * Format: https://<bucket>.<region>.your-objectstorage.com/<filename>
   */
  extractKeyFromUrl(url: string): string | null {
    try {
      // The endpoint already includes the bucket: https://bucket.region.your-objectstorage.com
      const prefix = `${this.endpoint}/`;

      if (url.startsWith(prefix)) {
        return url.substring(prefix.length);
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to extract key from URL ${url}:`, error);
      return null;
    }
  }

  /**
   * Generate a unique file key for storage
   */
  generateFileKey(userId: number, originalName: string, prefix: string = 'avatars'): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';

    return `${prefix}/${userId}/${timestamp}-${randomString}.${extension}`;
  }

  /**
   * Test the connection to S3 Object Storage
   */
  async testConnection(): Promise<boolean> {
    try {
      this.logger.log('Testing connection to S3 Object Storage...');
      this.logger.log(`Endpoint: ${this.configService.get<string>('S3_ENDPOINT')}`);
      this.logger.log(`Bucket: ${this.bucketName}`);
      this.logger.log(`Access Key: ${this.configService.get<string>('S3_ACCESS_KEY')}`);

      const command = new HeadBucketCommand({
        Bucket: this.bucketName,
      });

      await this.s3Client.send(command);
      this.logger.log('S3 connection test successful!');
      return true;
    } catch (error) {
      this.logger.error('S3 connection test failed:', error);
      this.logger.error(`Error details:`, {
        message: error.message,
        statusCode: error.$metadata?.httpStatusCode,
        requestId: error.$metadata?.requestId,
      });
      return false;
    }
  }

  /**
   * Generate virtual-hosted-style URL
   * Format: https://<bucket>.<region>.your-objectstorage.com/<filename>
   */
  private generateVirtualHostedUrl(key: string): string {
    // The endpoint is already in the correct format: https://bucket.region.your-objectstorage.com
    const virtualHostedUrl = `${this.endpoint}/${key}`;

    this.logger.log(`Generated virtual-hosted URL: ${virtualHostedUrl}`);
    return virtualHostedUrl;
  }
}
