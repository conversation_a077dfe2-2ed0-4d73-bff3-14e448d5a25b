import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  BadRequestException,
  Inject,
  forwardRef,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiBody } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateUserDto, UpdateUserDto } from './dto/user.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { TrainDto } from './dto/training.dto';
import { Request } from 'express';
import { EnergyService } from './energy.service';
import { TransferMoneyDto } from './dto/transfer-money.dto';
import { UploadAvatarResponseDto } from './dto/upload-avatar.dto';
import { StorageService } from '../storage/storage.service';
import { WebDAVStorageService } from '../storage/webdav-storage.service';
import { Public } from 'src/common/decorators/public.decorator';

@ApiTags('Users')
@Controller('users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    private readonly storageService: StorageService,
    private readonly webdavStorageService: WebDAVStorageService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Get()
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Get all users' })
  findAll() {
    return this.userService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a user by ID' })
  findOne(@Param('id') id: string) {
    return this.userService.findOne(+id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a user by ID' })
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.update(+id, updateUserDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a user by ID' })
  remove(@Param('id') id: string) {
    return this.userService.remove(+id);
  }

  @Post('/train')
  @ApiOperation({
    summary: 'Initiate a training session for the authenticated user',
  })
  async train(
    @Body() trainDto: TrainDto,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.userService.train(userId.toString(), trainDto);
  }

  @Get('/profile')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: "Get the current user's profile" })
  @ApiResponse({
    status: 200,
    description: "Returns the current user's profile",
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Invalid user ID' })
  async getProfile(@Req() req: Request & { user: { userId: number } }) {
    // Make sure userId is a number before passing it to findOne
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.userService.findOne(userId);
  }

  @Get('/energy')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: "Get the current user's energy" })
  @ApiResponse({
    status: 200,
    description: "Returns the current user's updated energy",
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Invalid user ID' })
  async getEnergy(@Req() req: Request & { user: { userId: number } }) {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }

    // Update and get the user's energy
    const user = await this.energyService.updateEnergyById(userId);

    // Return only the energy-related information
    return {
      energy: user.energy,
      maxEnergy: user.isPremium ? 200 : 100,
      isPremium: user.isPremium,
      regenerationRate: user.isPremium ? 20 : 10, // Energy per 30 minutes
      regenerationInterval: 30, // Minutes
      lastUpdated: user.updatedAt,
    };
  }

  @Post('transfer-money')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Transfer money between users' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Transfer completed successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input (self-transfer, negative amount, etc.)',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'User not found or not active',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Insufficient funds',
  })
  async transferMoney(
    @Body() transferMoneyDto: TransferMoneyDto,
  ): Promise<void> {
    await this.userService.transferMoney(transferMoneyDto);
  }

  @Post('avatar')
  @UseGuards(AuthGuard)
  @UseInterceptors(FileInterceptor('avatar'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload user avatar' })
  @ApiBody({
    description: 'Avatar image file',
    schema: {
      type: 'object',
      properties: {
        avatar: {
          type: 'string',
          format: 'binary',
          description: 'Avatar image file (JPEG, PNG, or WebP, max 5MB)',
        },
      },
      required: ['avatar'],
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Avatar uploaded successfully',
    type: UploadAvatarResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid file or file too large',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async uploadAvatar(
    @UploadedFile() file: Express.Multer.File,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<UploadAvatarResponseDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const result = await this.userService.uploadAvatar(req.user.userId, file);

    return {
      avatarUrl: result.url,
      key: result.key,
      size: result.size,
      mimeType: result.mimeType,
    };
  }

  @Delete('avatar')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Delete user avatar' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Avatar deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'User does not have an avatar to delete',
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  async deleteAvatar(
    @Req() req: Request & { user: { userId: number } },
  ): Promise<void> {
    await this.userService.deleteAvatar(req.user.userId);
  }

  @Get('test-storage')
  @Public()
  @ApiOperation({ summary: 'Test Hetzner Storage Box S3 connection' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'S3 connection test result',
  })
  async testStorageConnection(): Promise<{ success: boolean; message: string }> {
    const success = await this.storageService.testConnection();
    return {
      success,
      message: success ? 'S3 connection successful' : 'S3 connection failed - check logs for details'
    };
  }

  @Get('test-webdav')
  @Public()
  @ApiOperation({ summary: 'Test Hetzner Storage Box WebDAV connection' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'WebDAV connection test result',
  })
  async testWebDAVConnection(): Promise<{ success: boolean; message: string }> {
    const success = await this.webdavStorageService.testConnection();
    return {
      success,
      message: success ? 'WebDAV connection successful' : 'WebDAV connection failed - check logs for details'
    };
  }
}
