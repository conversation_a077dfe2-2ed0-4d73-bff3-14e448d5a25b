import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { StorageService } from './storage.service';

describe('StorageService', () => {
  let service: StorageService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        'HETZNER_STORAGE_BUCKET': 'test-bucket',
        'HETZNER_STORAGE_ENDPOINT': 'https://test.example.com',
        'HETZNER_STORAGE_REGION': 'eu-central-1',
        'HETZNER_STORAGE_ACCESS_KEY': 'test-access-key',
        'HETZNER_STORAGE_SECRET_KEY': 'test-secret-key',
      };
      return config[key];
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StorageService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<StorageService>(StorageService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should generate unique file keys', () => {
    const userId = 123;
    const originalName = 'test-image.jpg';
    
    const key1 = service.generateFileKey(userId, originalName);
    const key2 = service.generateFileKey(userId, originalName);
    
    expect(key1).toMatch(/^avatars\/123\/\d+-[a-z0-9]+\.jpg$/);
    expect(key2).toMatch(/^avatars\/123\/\d+-[a-z0-9]+\.jpg$/);
    expect(key1).not.toBe(key2); // Should be unique
  });

  it('should extract key from URL correctly', () => {
    const testUrl = 'https://test.example.com/test-bucket/avatars/123/test-file.jpg';
    const key = service.extractKeyFromUrl(testUrl);
    
    expect(key).toBe('avatars/123/test-file.jpg');
  });

  it('should return null for invalid URL', () => {
    const invalidUrl = 'https://other-domain.com/file.jpg';
    const key = service.extractKeyFromUrl(invalidUrl);
    
    expect(key).toBeNull();
  });

  it('should throw error when missing configuration', () => {
    const mockConfigServiceMissingKeys = {
      get: jest.fn(() => undefined),
    };

    expect(() => {
      new StorageService(mockConfigServiceMissingKeys as any);
    }).toThrow('Missing required Hetzner Storage Box configuration');
  });
});
