import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { StorageService } from './storage.service';
import { FileUploadService } from './file-upload.service';
import { WebDAVStorageService } from './webdav-storage.service';

@Module({
  imports: [ConfigModule],
  providers: [StorageService, FileUploadService, WebDAVStorageService],
  exports: [StorageService, FileUploadService, WebDAVStorageService],
})
export class StorageModule {}
