# Profile Picture Upload Feature

This document describes the profile picture upload functionality implemented for the Warfront Nations application.

## Overview

Users can now upload profile pictures (avatars) to their accounts. The images are stored in S3 Object Storage and automatically processed for optimization.

## Features

- **File Upload**: Support for JPEG, PNG, and WebP image formats
- **Image Processing**: Automatic resizing and optimization using Sharp
- **Cloud Storage**: Files stored in S3 Object Storage
- **Security**: File type validation and size limits
- **API Endpoints**: RESTful endpoints for upload and deletion

## Configuration

Add the following environment variables to your `.env` file:

```env
# S3 Object Storage Configuration
S3_ENDPOINT=https://fsn1.your-objectstorage.com
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key
S3_BUCKET=warfront-nations
S3_REGION=eu-central
```

## API Endpoints

### Upload Avatar
- **POST** `/users/avatar`
- **Authentication**: Required (JWT)
- **Content-Type**: `multipart/form-data`
- **Body**: Form data with `avatar` field containing the image file

**Response:**
```json
{
  "avatarUrl": "https://storage.example.com/avatars/123/1234567890-abc123.jpg",
  "key": "avatars/123/1234567890-abc123.jpg",
  "size": 245760,
  "mimeType": "image/jpeg"
}
```

### Delete Avatar
- **DELETE** `/users/avatar`
- **Authentication**: Required (JWT)

**Response:** 204 No Content

## File Specifications

- **Maximum Size**: 5MB
- **Supported Formats**: JPEG, PNG, WebP
- **Output Format**: JPEG (optimized)
- **Maximum Dimensions**: 512x512 pixels
- **Quality**: 85%

## Implementation Details

### Modules Created

1. **StorageModule** (`src/storage/`)
   - `StorageService`: Handles S3 Object Storage operations
   - `FileUploadService`: Manages file validation and image processing

2. **Updated UserModule**
   - Added avatar upload/delete methods to `UserService`
   - Added endpoints to `UserController`
   - Updated `User` entity to support null avatar URLs

### Image Processing

Images are automatically processed using Sharp library:
- Resized to fit within 512x512 pixels
- Converted to JPEG format
- Compressed with 85% quality
- File signatures validated for security

### Storage Structure

Files are stored with the following naming convention:
```
avatars/{userId}/{timestamp}-{randomString}.jpg
```

Example: `avatars/123/1672531200000-abc123def456.jpg`

## Security Features

- **File Type Validation**: Only image files are accepted
- **File Signature Check**: Validates actual file content, not just extension
- **Size Limits**: 5MB maximum file size
- **Authentication**: All endpoints require valid JWT token
- **Unique Naming**: Prevents file conflicts and overwrites

## Error Handling

The system handles various error scenarios:
- Invalid file types
- Files too large
- Corrupted images
- Storage service failures
- Network issues

## Dependencies

New dependencies added:
- `multer@^2.0.0`: File upload handling
- `@types/multer`: TypeScript definitions
- `sharp`: Image processing
- `@aws-sdk/client-s3`: S3-compatible storage client

## Usage Example

### Frontend (JavaScript/TypeScript)

```javascript
const uploadAvatar = async (file) => {
  const formData = new FormData();
  formData.append('avatar', file);

  const response = await fetch('/users/avatar', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};
```

### cURL Example

```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "avatar=@/path/to/image.jpg" \
  http://localhost:3000/users/avatar
```

## Testing

To test the functionality:

1. Start the server with proper environment variables
2. Authenticate and get a JWT token
3. Use the upload endpoint with a valid image file
4. Verify the image is accessible via the returned URL
5. Test deletion endpoint

## Troubleshooting

Common issues and solutions:

1. **"Failed to upload file"**: Check Hetzner Storage Box credentials
2. **"Invalid file type"**: Ensure file is JPEG, PNG, or WebP
3. **"File too large"**: Reduce file size below 5MB
4. **"No file provided"**: Ensure form field name is 'avatar'
